#!/usr/bin/env python3
"""
Intelligent Context Selection Engine

This module implements an AI-powered system that uses the rich Mid-Level IR data
to intelligently select the most relevant code context for any given task.

Key Features:
- Risk-Aware Selection: Prioritize high-criticality entities for modification tasks
- Dependency-Driven Context: Include related entities based on call graphs
- Task-Specific Filtering: Different strategies for debugging vs feature development
- Token Budget Optimization: Maximize relevance within context window limits
"""

import json
import math
from typing import Dict, List, Tuple, Optional, Set, Any
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path


class TaskType(Enum):
    """Types of development tasks that require different context selection strategies."""
    DEBUGGING = "debugging"
    FEATURE_DEVELOPMENT = "feature_development"
    CODE_REVIEW = "code_review"
    REFACTORING = "refactoring"
    DOCUMENTATION = "documentation"
    TESTING = "testing"
    GENERAL_ANALYSIS = "general_analysis"


class ContextPriority(Enum):
    """Priority levels for context selection."""
    CRITICAL = "critical"      # Must include
    HIGH = "high"             # Very important
    MEDIUM = "medium"         # Moderately important
    LOW = "low"              # Nice to have
    OPTIONAL = "optional"     # Include if space allows


@dataclass
class ContextEntity:
    """Represents a code entity with context selection metadata."""
    module_name: str
    entity_name: str
    entity_type: str  # function, class, variable, constant
    file_path: str
    
    # From IR data
    criticality: str
    change_risk: str
    used_by: List[str] = field(default_factory=list)
    calls: List[str] = field(default_factory=list)
    side_effects: List[str] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    
    # Context selection metadata
    relevance_score: float = 0.0
    priority: ContextPriority = ContextPriority.OPTIONAL
    token_estimate: int = 0
    dependency_depth: int = 0
    
    # Source code content (populated when selected)
    source_code: Optional[str] = None
    line_range: Optional[Tuple[int, int]] = None


@dataclass
class ContextBundle:
    """A bundle of selected context entities optimized for a specific task."""
    task_description: str
    task_type: TaskType
    entities: List[ContextEntity]
    total_tokens: int
    selection_rationale: str
    dependency_map: Dict[str, List[str]] = field(default_factory=dict)
    
    def get_entities_by_priority(self, priority: ContextPriority) -> List[ContextEntity]:
        """Get entities filtered by priority level."""
        return [e for e in self.entities if e.priority == priority]
    
    def get_critical_entities(self) -> List[ContextEntity]:
        """Get entities marked as critical priority."""
        return self.get_entities_by_priority(ContextPriority.CRITICAL)


class IntelligentContextSelector:
    """
    AI-powered context selection engine that uses Mid-Level IR data to select
    the most relevant code context for any given development task.
    """
    
    def __init__(self, ir_data: Dict[str, Any], max_tokens: int = 8000):
        """
        Initialize the context selector with IR data.
        
        Args:
            ir_data: Mid-Level IR data containing modules, entities, and dependencies
            max_tokens: Maximum token budget for context selection
        """
        self.ir_data = ir_data
        self.max_tokens = max_tokens
        self.modules = ir_data.get('modules', [])
        
        # Build entity lookup maps for fast access
        self.entity_map = self._build_entity_map()
        self.dependency_graph = self._build_dependency_graph()
        self.reverse_dependency_graph = self._build_reverse_dependency_graph()
        
        # Task-specific scoring weights
        self.task_weights = {
            TaskType.DEBUGGING: {
                'criticality': 0.4,
                'error_handling': 0.3,
                'side_effects': 0.2,
                'dependency_depth': 0.1
            },
            TaskType.FEATURE_DEVELOPMENT: {
                'criticality': 0.3,
                'dependency_depth': 0.3,
                'change_risk': 0.2,
                'usage_frequency': 0.2
            },
            TaskType.REFACTORING: {
                'change_risk': 0.4,
                'criticality': 0.3,
                'dependency_depth': 0.2,
                'usage_frequency': 0.1
            },
            TaskType.CODE_REVIEW: {
                'criticality': 0.3,
                'change_risk': 0.3,
                'side_effects': 0.2,
                'error_handling': 0.2
            },
            TaskType.GENERAL_ANALYSIS: {
                'criticality': 0.25,
                'dependency_depth': 0.25,
                'change_risk': 0.25,
                'usage_frequency': 0.25
            }
        }
    
    def _build_entity_map(self) -> Dict[str, ContextEntity]:
        """Build a map of entity names to ContextEntity objects."""
        entity_map = {}
        
        for module in self.modules:
            module_name = module.get('name', '')
            file_path = module.get('file', '')
            
            for entity in module.get('entities', []):
                entity_name = entity.get('name', '')
                full_name = f"{module_name}.{entity_name}"
                
                context_entity = ContextEntity(
                    module_name=module_name,
                    entity_name=entity_name,
                    entity_type=entity.get('type', 'unknown'),
                    file_path=file_path,
                    criticality=entity.get('criticality', 'low'),
                    change_risk=entity.get('change_risk', 'low'),
                    used_by=entity.get('used_by', []),
                    calls=entity.get('calls', []),
                    side_effects=entity.get('side_effects', []),
                    errors=entity.get('errors', []),
                    token_estimate=self._estimate_entity_tokens(entity)
                )
                
                entity_map[full_name] = context_entity
                
        return entity_map
    
    def _build_dependency_graph(self) -> Dict[str, Set[str]]:
        """Build a graph of entity dependencies (what each entity calls)."""
        graph = {}
        
        for entity_name, entity in self.entity_map.items():
            graph[entity_name] = set()
            
            # Add direct function calls
            for call in entity.calls:
                # Try to resolve the call to a full entity name
                resolved_call = self._resolve_entity_name(call, entity.module_name)
                if resolved_call and resolved_call in self.entity_map:
                    graph[entity_name].add(resolved_call)
        
        return graph
    
    def _build_reverse_dependency_graph(self) -> Dict[str, Set[str]]:
        """Build a reverse dependency graph (what entities depend on each entity)."""
        reverse_graph = {}
        
        # Initialize empty sets
        for entity_name in self.entity_map:
            reverse_graph[entity_name] = set()
        
        # Build reverse relationships
        for entity_name, dependencies in self.dependency_graph.items():
            for dependency in dependencies:
                if dependency in reverse_graph:
                    reverse_graph[dependency].add(entity_name)
        
        return reverse_graph
    
    def _resolve_entity_name(self, call_name: str, current_module: str) -> Optional[str]:
        """Resolve a function call name to a full entity name."""
        # Try direct match first
        if call_name in self.entity_map:
            return call_name
        
        # Try with current module prefix
        full_name = f"{current_module}.{call_name}"
        if full_name in self.entity_map:
            return full_name
        
        # Try to find in other modules
        for entity_name in self.entity_map:
            if entity_name.endswith(f".{call_name}"):
                return entity_name
        
        return None
    
    def _estimate_entity_tokens(self, entity: Dict) -> int:
        """Estimate the number of tokens an entity would consume."""
        # Base token estimate based on entity type and metadata
        base_tokens = {
            'function': 50,
            'class': 100,
            'variable': 10,
            'constant': 10
        }
        
        entity_type = entity.get('type', 'function')
        tokens = base_tokens.get(entity_type, 50)
        
        # Add tokens for parameters
        params = entity.get('params', [])
        if isinstance(params, list):
            tokens += len(params) * 5
        
        # Add tokens for documentation
        doc = entity.get('doc', '')
        if doc:
            tokens += len(doc.split()) * 1.3  # Rough token estimate
        
        return int(tokens)

    def select_optimal_context(self, task_description: str, task_type: TaskType = TaskType.GENERAL_ANALYSIS,
                             focus_entities: Optional[List[str]] = None) -> ContextBundle:
        """
        Select the most relevant code context for a given task.

        Args:
            task_description: Natural language description of the task
            task_type: Type of development task (affects selection strategy)
            focus_entities: Optional list of specific entities to focus on

        Returns:
            ContextBundle containing the selected entities and metadata
        """
        print(f"🎯 Selecting optimal context for: {task_description}")
        print(f"   Task type: {task_type.value}")
        print(f"   Token budget: {self.max_tokens}")

        # Step 1: Score all entities for relevance
        scored_entities = self._score_entities_for_task(task_description, task_type, focus_entities)

        # Step 2: Select entities within token budget
        selected_entities = self._select_entities_within_budget(scored_entities, task_type)

        # Step 3: Enhance selection with dependency context
        enhanced_entities = self._enhance_with_dependency_context(selected_entities, task_type)

        # Step 4: Build the final context bundle
        context_bundle = self._build_context_bundle(
            task_description, task_type, enhanced_entities
        )

        print(f"✅ Context selection complete:")
        print(f"   Selected {len(context_bundle.entities)} entities")
        print(f"   Total tokens: {context_bundle.total_tokens}")
        print(f"   Critical entities: {len(context_bundle.get_critical_entities())}")

        return context_bundle

    def _score_entities_for_task(self, task_description: str, task_type: TaskType,
                                focus_entities: Optional[List[str]] = None) -> List[ContextEntity]:
        """Score all entities based on their relevance to the task."""
        scored_entities = []
        weights = self.task_weights.get(task_type, self.task_weights[TaskType.GENERAL_ANALYSIS])

        for entity_name, entity in self.entity_map.items():
            # Calculate relevance score
            score = self._calculate_relevance_score(entity, task_description, weights, focus_entities)

            # Update entity with score and priority
            entity.relevance_score = score
            entity.priority = self._determine_priority(entity, score, task_type)
            entity.dependency_depth = self._calculate_dependency_depth(entity_name)

            scored_entities.append(entity)

        # Sort by relevance score (highest first)
        scored_entities.sort(key=lambda e: e.relevance_score, reverse=True)

        return scored_entities

    def _calculate_relevance_score(self, entity: ContextEntity, task_description: str,
                                 weights: Dict[str, float], focus_entities: Optional[List[str]] = None) -> float:
        """Calculate a relevance score for an entity based on the task."""
        score = 0.0

        # Focus entity bonus
        if focus_entities:
            full_name = f"{entity.module_name}.{entity.entity_name}"
            if any(focus in full_name for focus in focus_entities):
                score += 2.0  # Strong bonus for focus entities

        # Criticality score
        criticality_scores = {'low': 0.2, 'medium': 0.6, 'high': 1.0}
        score += weights.get('criticality', 0.25) * criticality_scores.get(entity.criticality, 0.2)

        # Change risk score (higher risk = more important for some tasks)
        risk_scores = {'low': 0.2, 'medium': 0.6, 'high': 1.0}
        score += weights.get('change_risk', 0.25) * risk_scores.get(entity.change_risk, 0.2)

        # Usage frequency (how many entities use this one)
        usage_count = len(self.reverse_dependency_graph.get(f"{entity.module_name}.{entity.entity_name}", set()))
        usage_score = min(usage_count / 10.0, 1.0)  # Normalize to 0-1
        score += weights.get('usage_frequency', 0.25) * usage_score

        # Error handling relevance
        error_score = len(entity.errors) / 5.0  # Normalize
        score += weights.get('error_handling', 0.1) * min(error_score, 1.0)

        # Side effects relevance
        side_effect_score = len(entity.side_effects) / 3.0  # Normalize
        score += weights.get('side_effects', 0.1) * min(side_effect_score, 1.0)

        # Dependency depth (deeper = potentially more important)
        depth_score = min(entity.dependency_depth / 5.0, 1.0)
        score += weights.get('dependency_depth', 0.25) * depth_score

        # Text relevance (simple keyword matching)
        text_score = self._calculate_text_relevance(entity, task_description)
        score += 0.3 * text_score  # Text relevance bonus

        return score

    def _calculate_text_relevance(self, entity: ContextEntity, task_description: str) -> float:
        """Calculate text-based relevance between entity and task description."""
        task_words = set(task_description.lower().split())

        # Check entity name
        entity_words = set(entity.entity_name.lower().replace('_', ' ').split())
        name_overlap = len(task_words.intersection(entity_words)) / max(len(task_words), 1)

        # Check module name
        module_words = set(entity.module_name.lower().replace('_', ' ').split())
        module_overlap = len(task_words.intersection(module_words)) / max(len(task_words), 1)

        return (name_overlap * 0.7 + module_overlap * 0.3)

    def _determine_priority(self, entity: ContextEntity, score: float, task_type: TaskType) -> ContextPriority:
        """Determine the priority level for an entity based on its score and task type."""
        if score >= 2.0:
            return ContextPriority.CRITICAL
        elif score >= 1.5:
            return ContextPriority.HIGH
        elif score >= 1.0:
            return ContextPriority.MEDIUM
        elif score >= 0.5:
            return ContextPriority.LOW
        else:
            return ContextPriority.OPTIONAL

    def _calculate_dependency_depth(self, entity_name: str) -> int:
        """Calculate the dependency depth of an entity (how deep in the call graph)."""
        visited = set()

        def dfs_depth(name: str, current_depth: int) -> int:
            if name in visited or current_depth > 10:  # Prevent infinite recursion
                return current_depth

            visited.add(name)
            dependencies = self.dependency_graph.get(name, set())

            if not dependencies:
                return current_depth

            max_depth = current_depth
            for dep in dependencies:
                depth = dfs_depth(dep, current_depth + 1)
                max_depth = max(max_depth, depth)

            return max_depth

        return dfs_depth(entity_name, 0)

    def _select_entities_within_budget(self, scored_entities: List[ContextEntity],
                                     task_type: TaskType) -> List[ContextEntity]:
        """Select entities that fit within the token budget, prioritizing by relevance and priority."""
        selected = []
        total_tokens = 0

        # Reserve tokens for critical entities first
        critical_entities = [e for e in scored_entities if e.priority == ContextPriority.CRITICAL]
        for entity in critical_entities:
            if total_tokens + entity.token_estimate <= self.max_tokens:
                selected.append(entity)
                total_tokens += entity.token_estimate

        # Add high priority entities
        high_entities = [e for e in scored_entities if e.priority == ContextPriority.HIGH and e not in selected]
        for entity in high_entities:
            if total_tokens + entity.token_estimate <= self.max_tokens:
                selected.append(entity)
                total_tokens += entity.token_estimate

        # Add medium priority entities if space allows
        medium_entities = [e for e in scored_entities if e.priority == ContextPriority.MEDIUM and e not in selected]
        for entity in medium_entities:
            if total_tokens + entity.token_estimate <= self.max_tokens:
                selected.append(entity)
                total_tokens += entity.token_estimate

        # Fill remaining space with low priority entities
        low_entities = [e for e in scored_entities if e.priority == ContextPriority.LOW and e not in selected]
        for entity in low_entities:
            if total_tokens + entity.token_estimate <= self.max_tokens:
                selected.append(entity)
                total_tokens += entity.token_estimate

        return selected

    def _enhance_with_dependency_context(self, selected_entities: List[ContextEntity],
                                       task_type: TaskType) -> List[ContextEntity]:
        """Enhance the selection by adding important dependency context."""
        enhanced = selected_entities.copy()
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in selected_entities}

        # Calculate remaining token budget
        current_tokens = sum(e.token_estimate for e in enhanced)
        remaining_tokens = self.max_tokens - current_tokens

        # Add critical dependencies
        for entity in selected_entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"

            # Add entities that this one depends on (calls)
            dependencies = self.dependency_graph.get(entity_name, set())
            for dep_name in dependencies:
                if dep_name not in selected_names and dep_name in self.entity_map:
                    dep_entity = self.entity_map[dep_name]
                    if (remaining_tokens >= dep_entity.token_estimate and
                        dep_entity.criticality in ['high', 'medium']):
                        enhanced.append(dep_entity)
                        selected_names.add(dep_name)
                        remaining_tokens -= dep_entity.token_estimate

            # Add entities that depend on this one (reverse dependencies)
            reverse_deps = self.reverse_dependency_graph.get(entity_name, set())
            for rev_dep_name in reverse_deps:
                if rev_dep_name not in selected_names and rev_dep_name in self.entity_map:
                    rev_dep_entity = self.entity_map[rev_dep_name]
                    if (remaining_tokens >= rev_dep_entity.token_estimate and
                        rev_dep_entity.criticality in ['high', 'medium']):
                        enhanced.append(rev_dep_entity)
                        selected_names.add(rev_dep_name)
                        remaining_tokens -= rev_dep_entity.token_estimate

        return enhanced

    def _build_context_bundle(self, task_description: str, task_type: TaskType,
                            entities: List[ContextEntity]) -> ContextBundle:
        """Build the final context bundle with metadata and rationale."""
        total_tokens = sum(e.token_estimate for e in entities)

        # Build dependency map for the selected entities
        dependency_map = {}
        for entity in entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"
            deps = self.dependency_graph.get(entity_name, set())
            # Only include dependencies that are also in the selected set
            selected_names = {f"{e.module_name}.{e.entity_name}" for e in entities}
            filtered_deps = [dep for dep in deps if dep in selected_names]
            dependency_map[entity_name] = filtered_deps

        # Generate selection rationale
        rationale = self._generate_selection_rationale(entities, task_type, total_tokens)

        return ContextBundle(
            task_description=task_description,
            task_type=task_type,
            entities=entities,
            total_tokens=total_tokens,
            selection_rationale=rationale,
            dependency_map=dependency_map
        )

    def _generate_selection_rationale(self, entities: List[ContextEntity],
                                    task_type: TaskType, total_tokens: int) -> str:
        """Generate a human-readable rationale for the context selection."""
        priority_counts = {}
        for priority in ContextPriority:
            count = len([e for e in entities if e.priority == priority])
            if count > 0:
                priority_counts[priority.value] = count

        criticality_counts = {}
        for criticality in ['low', 'medium', 'high']:
            count = len([e for e in entities if e.criticality == criticality])
            if count > 0:
                criticality_counts[criticality] = count

        rationale = f"""Context Selection Rationale for {task_type.value}:

Selected {len(entities)} entities using {total_tokens} tokens ({(total_tokens/self.max_tokens)*100:.1f}% of budget).

Priority Distribution:
{chr(10).join(f"  - {priority}: {count} entities" for priority, count in priority_counts.items())}

Criticality Distribution:
{chr(10).join(f"  - {criticality}: {count} entities" for criticality, count in criticality_counts.items())}

Selection Strategy:
- Prioritized entities with high relevance scores
- Included critical dependencies and reverse dependencies
- Optimized for {task_type.value} task requirements
- Maintained token budget constraints"""

        return rationale

    def get_entity_details(self, entity_name: str) -> Optional[ContextEntity]:
        """Get detailed information about a specific entity."""
        return self.entity_map.get(entity_name)

    def get_related_entities(self, entity_name: str, max_depth: int = 2) -> List[str]:
        """Get entities related to the given entity through dependencies."""
        related = set()
        visited = set()

        def explore_dependencies(name: str, depth: int):
            if depth > max_depth or name in visited:
                return

            visited.add(name)

            # Add direct dependencies
            deps = self.dependency_graph.get(name, set())
            for dep in deps:
                if dep in self.entity_map:
                    related.add(dep)
                    explore_dependencies(dep, depth + 1)

            # Add reverse dependencies
            reverse_deps = self.reverse_dependency_graph.get(name, set())
            for rev_dep in reverse_deps:
                if rev_dep in self.entity_map:
                    related.add(rev_dep)
                    explore_dependencies(rev_dep, depth + 1)

        explore_dependencies(entity_name, 0)
        return list(related)

    def analyze_context_quality(self, context_bundle: ContextBundle) -> Dict[str, Any]:
        """Analyze the quality and completeness of a context bundle."""
        entities = context_bundle.entities

        # Calculate coverage metrics
        total_entities = len(self.entity_map)
        selected_entities = len(entities)
        coverage_percentage = (selected_entities / total_entities) * 100

        # Analyze priority distribution
        priority_dist = {}
        for priority in ContextPriority:
            count = len([e for e in entities if e.priority == priority])
            priority_dist[priority.value] = count

        # Analyze criticality distribution
        criticality_dist = {}
        for criticality in ['low', 'medium', 'high']:
            count = len([e for e in entities if e.criticality == criticality])
            criticality_dist[criticality] = count

        # Calculate dependency completeness
        missing_deps = 0
        total_deps = 0
        selected_names = {f"{e.module_name}.{e.entity_name}" for e in entities}

        for entity in entities:
            entity_name = f"{entity.module_name}.{entity.entity_name}"
            deps = self.dependency_graph.get(entity_name, set())
            total_deps += len(deps)
            missing_deps += len([d for d in deps if d not in selected_names])

        dependency_completeness = ((total_deps - missing_deps) / max(total_deps, 1)) * 100

        return {
            'coverage_percentage': coverage_percentage,
            'selected_entities': selected_entities,
            'total_entities': total_entities,
            'priority_distribution': priority_dist,
            'criticality_distribution': criticality_dist,
            'dependency_completeness': dependency_completeness,
            'token_utilization': (context_bundle.total_tokens / self.max_tokens) * 100,
            'average_relevance_score': sum(e.relevance_score for e in entities) / len(entities) if entities else 0
        }


def test_intelligent_context_selector():
    """Test the Intelligent Context Selection Engine with sample data."""
    print("🧪 Testing Intelligent Context Selection Engine")
    print("=" * 60)

    # Load IR data
    try:
        # Try to load existing IR data
        import os
        ir_files = [f for f in os.listdir('.') if f.endswith('_ir.json')]

        if ir_files:
            ir_file = ir_files[0]  # Use the first IR file found
            print(f"📁 Loading IR data from: {ir_file}")

            with open(ir_file, 'r') as f:
                ir_data = json.load(f)
        else:
            # Generate new IR data if none exists
            print("📁 No existing IR data found, generating new data...")
            from aider_integration_service import AiderIntegrationService

            service = AiderIntegrationService()
            ir_data = service.generate_mid_level_ir(os.getcwd())

            # Save for future use
            with open('test_ir.json', 'w') as f:
                json.dump(ir_data, f, indent=2)
            print("💾 Saved IR data to test_ir.json")

        print(f"✅ Loaded IR data with {len(ir_data.get('modules', []))} modules")

        # Create context selector
        selector = IntelligentContextSelector(ir_data, max_tokens=4000)
        print(f"🎯 Created context selector with {len(selector.entity_map)} entities")

        # Test different task types
        test_cases = [
            ("Fix a bug in the file parsing logic", TaskType.DEBUGGING, ["file", "parse"]),
            ("Add a new feature for code analysis", TaskType.FEATURE_DEVELOPMENT, ["analysis", "code"]),
            ("Refactor the dependency management system", TaskType.REFACTORING, ["dependency"]),
            ("Review the error handling implementation", TaskType.CODE_REVIEW, ["error", "exception"])
        ]

        for i, (task_desc, task_type, focus_entities) in enumerate(test_cases, 1):
            print(f"\n🔍 Test Case {i}: {task_desc}")
            print(f"   Task Type: {task_type.value}")

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_desc,
                task_type=task_type,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            print(f"\n📊 Context Quality Analysis:")
            print(f"   Token Utilization: {quality_analysis['token_utilization']:.1f}%")
            print(f"   Dependency Completeness: {quality_analysis['dependency_completeness']:.1f}%")
            print(f"   Average Relevance Score: {quality_analysis['average_relevance_score']:.2f}")

            # Show top entities
            top_entities = sorted(context_bundle.entities, key=lambda e: e.relevance_score, reverse=True)[:3]
            print(f"\n🏆 Top 3 Selected Entities:")
            for j, entity in enumerate(top_entities, 1):
                print(f"   {j}. {entity.module_name}.{entity.entity_name} "
                      f"(score: {entity.relevance_score:.2f}, priority: {entity.priority.value})")

        print(f"\n✅ Intelligent Context Selection Engine test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    test_intelligent_context_selector()
