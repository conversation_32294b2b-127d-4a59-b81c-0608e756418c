# CONTEXT_REQUEST Protocol

The CONTEXT_REQUEST protocol is a powerful enhancement to the Aider system that enables the LLM to request specific code symbols (functions, methods, classes) and receive surgically extracted implementations along with their key dependencies. This approach provides more targeted and relevant code context compared to requesting entire files.

## Overview

The CONTEXT_REQUEST protocol works as follows:

1. The LLM identifies specific code symbols it needs to understand to answer the user's question
2. The LLM sends a CONTEXT_REQUEST with the symbols of interest
3. The system uses the surgical extraction functionality to extract the requested symbols and their dependencies
4. The system generates an augmented prompt with the extracted context
5. The LLM receives the augmented prompt and provides a more informed response to the user

## CONTEXT_REQUEST Format

The LLM can request specific code context using the following format:

```json
{CONTEXT_REQUEST: { 
  "original_user_query_context": "Brief description of what the user is asking about",
  "symbols_of_interest": [
    {"type": "method_definition", "name": "ClassName.method_name", "file_hint": "path/to/file.py"},
    {"type": "class_definition", "name": "ClassName", "file_hint": "path/to/file.py"}
  ],
  "reason_for_request": "Why you need this context to answer the user's question"
}}
```

### Parameters

- `original_user_query_context`: A brief description of what the user is asking about
- `symbols_of_interest`: An array of symbols to extract
  - `type`: The type of symbol (method_definition, class_definition, function_definition, etc.)
  - `name`: The name of the symbol (e.g., "ClassName.method_name")
  - `file_hint`: Optional hint about which file contains the symbol
- `reason_for_request`: Why the LLM needs this context to answer the user's question

## Guidelines for Using CONTEXT_REQUEST

The LLM is instructed to follow these guidelines when using the CONTEXT_REQUEST protocol:

1. Only request symbols that are directly relevant to answering the user's question
2. Be specific about the symbols needed (use fully qualified names when possible)
3. Provide file hints when known to help locate the symbols
4. Explain why this context is needed in the reason_for_request field
5. Try to get all related symbols in one request, but multiple requests are allowed if necessary

## Implementation Details

The CONTEXT_REQUEST functionality is implemented through the following components:

1. `context_request_handler.py`: Handles parsing and processing context requests
2. `aider_template_renderer.py`: Formats the extracted context into a well-structured prompt
3. `aider_context_request_integration.py`: Integrates the context request handler with Aider

The system uses the surgical extraction functionality to extract the requested symbols and their dependencies, providing a more targeted and relevant code context compared to requesting entire files.

## Benefits

The CONTEXT_REQUEST protocol offers several benefits:

1. **Reduced Token Usage**: By extracting only the relevant code symbols instead of entire files, the system uses fewer tokens in the LLM context window
2. **Improved Relevance**: The LLM receives only the code that is directly relevant to the user's question
3. **Better Understanding**: The LLM can see the implementation details of specific functions, methods, or classes without being overwhelmed by irrelevant code
4. **Dependency Awareness**: The system includes key dependencies of the requested symbols, providing a more complete understanding of the code

## Testing

You can test the CONTEXT_REQUEST functionality using the `test_context_request.py` script:

```bash
python test_context_request.py
```

This script creates a sample context request, processes it, and displays the augmented prompt that would be sent to the LLM.

## Integration with Aider

The CONTEXT_REQUEST protocol is fully integrated with the Aider system:

1. The LLM is instructed about the CONTEXT_REQUEST protocol in the system prompt
2. The `process_context_requests` method in `base_coder.py` handles context requests in the LLM response
3. The context request handler extracts the requested symbols and generates an augmented prompt
4. The augmented prompt is sent back to the LLM for a more informed response

## Limitations

- The system is limited to 3 context requests per user query to prevent excessive token usage
- The surgical extraction system requires accurate repository mapping data to function properly
- Some complex code structures may not be perfectly extracted, but the system aims to provide the most relevant context possible
