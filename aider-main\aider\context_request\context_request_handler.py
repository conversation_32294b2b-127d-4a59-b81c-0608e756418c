#!/usr/bin/env python

import os
import re
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field

from .aider_integration_service import AiderIntegrationService


@dataclass
class SymbolRequest:
    """Represents a symbol requested by the LLM."""
    type: str  # method_definition, class_definition, function_definition, etc.
    name: str  # The name of the symbol, e.g., "AuthService.login_user"
    file_hint: Optional[str] = None  # Optional hint about which file contains the symbol


@dataclass
class ContextRequest:
    """Represents a context request from the LLM."""
    original_user_query_context: str
    symbols_of_interest: List[SymbolRequest]
    reason_for_request: str = ""  # Make this optional with default value


class ContextRequestHandler:
    """
    Handles context requests from the LLM, extracting the requested symbols
    and their dependencies using the surgical extraction system.
    """

    def __init__(self, project_path: str, aider_service: Optional[AiderIntegrationService] = None):
        """
        Initialize the context request handler.

        Args:
            project_path: Path to the project root
            aider_service: Optional AiderIntegrationService instance
        """
        self.project_path = project_path
        self.aider_service = aider_service or AiderIntegrationService()
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_ttl = 3600  # 1 hour

    def _get_from_cache(self, cache_key: str) -> Any:
        """Get a value from the cache if it exists and is not expired."""
        if cache_key in self.cache:
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if (timestamp + self.cache_ttl) > time.time():
                return self.cache[cache_key]
        return None

    def _update_cache(self, cache_key: str, value: Any) -> None:
        """Update the cache with a new value."""
        self.cache[cache_key] = value
        self.cache_timestamps[cache_key] = time.time()

    def parse_context_request(self, request_text: str) -> Optional[ContextRequest]:
        """
        Parse a context request from the LLM response.

        Args:
            request_text: The text containing the context request

        Returns:
            A ContextRequest object or None if the request is invalid
        """
        try:
            # Extract the JSON object from the request text using more robust patterns
            # First try the standard pattern with double closing braces
            pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}\}'
            match = re.search(pattern, request_text, re.DOTALL)
            if not match:
                # Try with a single closing brace (more common format)
                pattern = r'\{CONTEXT_REQUEST:\s*(.*?)\}'
                match = re.search(pattern, request_text, re.DOTALL)
                if not match:
                    # Try with any content after CONTEXT_REQUEST:
                    pattern = r'\{CONTEXT_REQUEST:\s*(.*)'
                    match = re.search(pattern, request_text, re.DOTALL)
                    if not match:
                        # Try with just the keyword
                        pattern = r'\{CONTEXT_REQUEST'
                        if re.search(pattern, request_text, re.DOTALL):
                            # Found the keyword but couldn't extract content properly
                            print("Found CONTEXT_REQUEST keyword but couldn't extract content properly")
                            # Create a minimal valid context request
                            return ContextRequest(
                                original_user_query_context="Extracted from malformed CONTEXT_REQUEST",
                                symbols_of_interest=[],
                                reason_for_request="Malformed CONTEXT_REQUEST detected"
                            )
                        return None

            # Get the matched content
            json_str = match.group(1).strip()

            # Clean up the JSON string
            # Remove any trailing }} that might be part of the CONTEXT_REQUEST format
            json_str = json_str.rstrip('}')

            # Ensure it's a valid JSON object
            if not json_str.startswith('{'):
                json_str = '{' + json_str
            if not json_str.endswith('}'):
                json_str = json_str + '}'

            # Replace any escaped quotes
            json_str = json_str.replace('\\"', '"')

            # Fix backslash escaping issues in file paths
            # Replace backslashes with forward slashes in file paths
            json_str = re.sub(r'"([^"]*\\[^"]*)"', lambda m: '"' + m.group(1).replace('\\', '/') + '"', json_str)

            # Try to parse the JSON
            try:
                request_data = json.loads(json_str)
            except json.JSONDecodeError as e:
                print(f"JSON parsing error: {e}")
                print(f"Problematic JSON: {json_str}")

                # Try to fix common JSON formatting issues
                # Replace single quotes with double quotes
                json_str = json_str.replace("'", '"')
                # Fix unquoted keys
                json_str = re.sub(r'(\w+):', r'"\1":', json_str)
                # Fix backslash escaping issues again after quote replacement
                json_str = re.sub(r'"([^"]*\\[^"]*)"', lambda m: '"' + m.group(1).replace('\\', '/') + '"', json_str)

                try:
                    request_data = json.loads(json_str)
                    print("✅ Fixed JSON parsing after format corrections")
                except json.JSONDecodeError as e2:
                    print(f"❌ Still failed after corrections: {e2}")
                    print(f"Final JSON attempt: {json_str}")
                    raise e2

            # Create the ContextRequest object
            symbols = []
            symbols_of_interest = request_data.get('symbols_of_interest', [])

            # Check if symbols_of_interest is in the wrong format (array of strings instead of objects)
            if symbols_of_interest and isinstance(symbols_of_interest[0], str):
                # LLM used wrong format - provide guidance
                print("❌ CONTEXT_REQUEST FORMAT ERROR:")
                print("symbols_of_interest should be an array of objects, not strings!")
                print("❌ Wrong format:", symbols_of_interest)
                print("✅ Correct format should be:")
                print('[{"type": "method_definition", "name": "function_name", "file_hint": "path/to/file.py"}]')

                # Try to convert the strings to proper format by guessing
                for symbol_name in symbols_of_interest:
                    # Skip generic terms that aren't actual symbols
                    if symbol_name in ['position', 'conditions', 'exit', 'trading', 'close']:
                        continue

                    # Determine the type based on naming patterns
                    symbol_type = "function_definition"
                    if symbol_name and symbol_name[0].isupper():
                        symbol_type = "class_definition"
                    elif '.' in symbol_name:
                        symbol_type = "method_definition"

                    symbols.append(SymbolRequest(
                        type=symbol_type,
                        name=symbol_name,
                        file_hint=None  # No file hint available from string format
                    ))
            else:
                # Correct format - process normally
                for symbol_data in symbols_of_interest:
                    if isinstance(symbol_data, dict):
                        # Handle both old file_hint format and new directory_name/file_name format
                        file_hint = symbol_data.get('file_hint')
                        directory_name = symbol_data.get('directory_name')
                        file_name = symbol_data.get('file_name')

                        # If using new format, construct file_hint from directory_name and file_name
                        if directory_name and file_name:
                            file_hint = f"{directory_name}/{file_name}"
                        elif directory_name and not file_name:
                            file_hint = directory_name
                        elif file_name and not directory_name:
                            file_hint = file_name
                        # Otherwise use the existing file_hint value

                        symbols.append(SymbolRequest(
                            type=symbol_data.get('type', 'unknown'),
                            name=symbol_data.get('name', ''),
                            file_hint=file_hint
                        ))
                    else:
                        print(f"Warning: Invalid symbol data format: {symbol_data}")

            # Provide a meaningful default for reason_for_request if missing
            reason_for_request = request_data.get('reason_for_request', '')
            if not reason_for_request:
                # Generate a default reason based on the user query
                user_query = request_data.get('original_user_query_context', '')
                if user_query:
                    reason_for_request = f"To answer the user's question: {user_query}"
                else:
                    reason_for_request = "To provide code context for the user's query"

            return ContextRequest(
                original_user_query_context=request_data.get('original_user_query_context', ''),
                symbols_of_interest=symbols,
                reason_for_request=reason_for_request
            )
        except Exception as e:
            print(f"Error parsing context request: {e}")
            return None

    def _find_file_for_symbol(self, symbol: SymbolRequest) -> Optional[str]:
        """
        Find the file that contains the requested symbol.

        Args:
            symbol: The symbol to find

        Returns:
            The path to the file containing the symbol, or None if not found
        """
        # Check if the symbol is referring to an installed Aider package (not local development)
        if symbol.name.startswith('aider.') and not (symbol.file_hint and ('aider-main' in symbol.file_hint or 'aider/' in symbol.file_hint)):
            print(f"Warning: Symbol {symbol.name} appears to be part of the installed Aider package, not your project.")
            print(f"The system cannot access installed Aider package files directly.")
            return None

        # If we have a file hint, try that first
        if symbol.file_hint:
            # Check if the file exists
            file_path = os.path.join(self.project_path, symbol.file_hint)
            if os.path.exists(file_path):
                return symbol.file_hint
            else:
                print(f"Warning: File hint {symbol.file_hint} does not exist at {file_path}")

                # Try to find the file using RepoMap-style discovery
                discovered_file = self._discover_file_like_repomap(symbol.file_hint)
                if discovered_file:
                    rel_path = os.path.relpath(discovered_file, self.project_path)
                    print(f"Found alternative file at: {rel_path}")
                    return rel_path

        # Extract the symbol name (handle class.method format)
        symbol_parts = symbol.name.split('.')
        if len(symbol_parts) > 1:
            # This is a class.method or module.function format
            class_name = symbol_parts[0]
            method_name = symbol_parts[1]

            # Check if this is an Aider package class
            if class_name == 'aider' or class_name.startswith('aider.'):
                print(f"Warning: Class {class_name} appears to be part of the Aider package, not your project.")
                print(f"The system cannot access Aider package files directly.")
                return None

            # Try to find the file defining the class
            class_file = self.aider_service.find_file_defining_symbol(self.project_path, class_name)
            if class_file:
                return class_file

        # Try to find the file defining the symbol directly
        symbol_name = symbol_parts[-1]  # Use the last part of the symbol name
        symbol_file = self.aider_service.find_file_defining_symbol(self.project_path, symbol_name)
        if symbol_file:
            return symbol_file

        print(f"Warning: Could not find file for symbol: {symbol.name}")
        return None

    def _discover_file_like_repomap(self, file_hint: str) -> Optional[str]:
        """
        Discover files using the same logic as RepoMap to ensure consistency.
        This replicates the file discovery mechanism from aider.repomap.
        """
        try:
            # Normalize the file hint (handle both forward and backward slashes)
            normalized_hint = file_hint.replace('\\', '/').strip('/')
            hint_parts = normalized_hint.split('/')
            target_filename = hint_parts[-1]
            target_dir_parts = hint_parts[:-1] if len(hint_parts) > 1 else []

            # Walk the directory tree like RepoMap does
            for root, dirs, files in os.walk(self.project_path):
                # Skip hidden directories and common directories to ignore (like RepoMap)
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', 'venv', '.git', '__pycache__']]

                # Check if target file is in this directory
                if target_filename in files:
                    candidate_path = os.path.join(root, target_filename)
                    rel_path = os.path.relpath(candidate_path, self.project_path)

                    # Normalize the relative path for comparison
                    normalized_rel_path = rel_path.replace('\\', '/').strip('/')

                    # Exact match
                    if normalized_rel_path == normalized_hint:
                        return candidate_path

                    # If we have directory parts, check if they match
                    if target_dir_parts:
                        rel_parts = normalized_rel_path.split('/')[:-1]  # Exclude filename

                        # Check if the directory structure matches (partial or full)
                        if self._directory_parts_match(target_dir_parts, rel_parts):
                            return candidate_path
                    else:
                        # No directory specified, just filename - return first match
                        return candidate_path

            return None

        except Exception as e:
            print(f"Warning: Error in file discovery for {file_hint}: {e}")
            return None

    def _directory_parts_match(self, target_parts: list, candidate_parts: list) -> bool:
        """
        Check if directory parts match, allowing for partial matches.
        This handles cases where the hint might be a subdirectory path.
        """
        if not target_parts:
            return True

        if len(target_parts) > len(candidate_parts):
            return False

        # Convert to strings for easier comparison
        target_str = '/'.join(target_parts)
        candidate_str = '/'.join(candidate_parts)

        # Check if target is a substring of candidate (anywhere in the path)
        # This allows for both prefix and suffix matching
        return target_str in candidate_str or candidate_str.endswith(target_str) or candidate_str.startswith(target_str)

    def _extract_symbol_content(self, symbol: SymbolRequest) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Extract the content of a symbol using the surgical extraction system.

        Args:
            symbol: The symbol to extract

        Returns:
            A tuple of (file_path, symbol_name, content) or (None, None, None) if extraction failed
        """
        # Find the file containing the symbol
        file_path = self._find_file_for_symbol(symbol)
        if not file_path:
            return None, None, None

        # Extract the symbol name (handle class.method format)
        symbol_parts = symbol.name.split('.')
        if len(symbol_parts) > 1:
            # This is a class.method or module.function format
            class_name = symbol_parts[0]
            method_name = symbol_parts[1]

            # Try to extract the method content using the full qualified name
            content = self.aider_service.extract_symbol_content(symbol.name, file_path, self.project_path)
            if content:
                return file_path, symbol.name, content

            # Try to extract the method content
            content = self.aider_service.extract_symbol_content(method_name, file_path, self.project_path)
            if content:
                return file_path, method_name, content

            # If that fails, try to extract the class content
            content = self.aider_service.extract_symbol_content(class_name, file_path, self.project_path)
            if content:
                return file_path, class_name, content
        else:
            # This is a simple symbol name
            symbol_name = symbol_parts[0]
            content = self.aider_service.extract_symbol_content(symbol_name, file_path, self.project_path)
            if content:
                return file_path, symbol_name, content

        return None, None, None

    def process_context_request(self, request: ContextRequest) -> Dict[str, Any]:
        """
        Process a context request, extracting the requested symbols and their dependencies.

        Args:
            request: The context request to process

        Returns:
            A dictionary containing the extracted context
        """
        # Create a cache key for this request
        cache_key = f"context_request:{','.join([s.name for s in request.symbols_of_interest])}"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            return cached_result

        result = {
            "original_user_query_context": request.original_user_query_context,
            "reason_for_request": request.reason_for_request,
            "extracted_symbols": [],
            "dependency_snippets": [],
            "not_found_symbols": []  # Track symbols that couldn't be found
        }

        # Process each requested symbol
        for symbol in request.symbols_of_interest:
            # Check if the symbol is referring to an installed Aider package (not local development)
            if symbol.name.startswith('aider.') and not (symbol.file_hint and ('aider-main' in symbol.file_hint or 'aider/' in symbol.file_hint)):
                print(f"Warning: Symbol {symbol.name} appears to be part of the installed Aider package, not your project.")
                result["not_found_symbols"].append({
                    "symbol_name": symbol.name,
                    "reason": "Symbol appears to be part of the installed Aider package, not your project."
                })
                continue

            file_path, symbol_name, content = self._extract_symbol_content(symbol)
            if not file_path or not symbol_name or not content:
                result["not_found_symbols"].append({
                    "symbol_name": symbol.name,
                    "reason": "Could not find or extract the symbol content."
                })
                continue

            try:
                # Extract essential imports
                try:
                    essential_imports = self.aider_service.extract_essential_imports(self.project_path, file_path)
                except Exception as e:
                    print(f"Warning: Could not extract essential imports for {symbol.name}: {e}")
                    essential_imports = ""

                # Extract containing class signature if it's a method
                containing_class = None
                if '.' in symbol.name:
                    try:
                        containing_class = self.aider_service.extract_containing_class(self.project_path, file_path, symbol_name)
                    except Exception as e:
                        print(f"Warning: Could not extract containing class for {symbol.name}: {e}")

                # Extract usage contexts (temporarily disabled to avoid scanning entire repository)
                # TODO: Re-enable with smarter file filtering to avoid reading non-existent files
                usage_contexts = []
                # try:
                #     usage_contexts = self.aider_service.extract_usage_contexts(self.project_path, symbol_name, file_path)
                # except Exception as e:
                #     print(f"Warning: Could not extract usage contexts for {symbol.name}: {e}")
                #     usage_contexts = []

                # Add the extracted symbol to the result
                result["extracted_symbols"].append({
                    "symbol_name": symbol.name,
                    "file_path": file_path,
                    "content": content,
                    "essential_imports": essential_imports,
                    "containing_class": containing_class
                })

                # Add dependency snippets
                for usage in usage_contexts[:3]:  # Limit to 3 usage examples
                    result["dependency_snippets"].append({
                        "file_path": usage.get("file_path", ""),
                        "symbol_name": usage.get("symbol_name", ""),
                        "content": usage.get("content", ""),
                        "usage_type": usage.get("usage_type", "unknown")
                    })
            except Exception as e:
                print(f"Error processing symbol {symbol.name}: {e}")
                result["not_found_symbols"].append({
                    "symbol_name": symbol.name,
                    "reason": f"Error processing symbol: {str(e)}"
                })

        # Cache the result
        self._update_cache(cache_key, result)

        return result
