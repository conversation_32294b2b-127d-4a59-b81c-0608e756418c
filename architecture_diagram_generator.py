#!/usr/bin/env python3
"""
System Architecture Diagram Generator

Generates comprehensive system architecture diagrams from Mid-Level IR JSON data.
Creates multiple visualization types including system overview, dependency graphs,
and component architecture diagrams using Mermaid.

Usage:
    python architecture_diagram_generator.py [ir_file] [--output-dir diagrams]
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any
from collections import defaultdict, Counter
import re


class ArchitectureDiagramGenerator:
    """
    Generates system architecture diagrams from Mid-Level IR data.
    
    Creates multiple types of diagrams:
    - System Overview: High-level module relationships
    - Dependency Graph: Module dependencies with strength
    - Component Architecture: Logical groupings and layers
    - Critical Path: High-criticality components
    """
    
    def __init__(self, ir_data: Dict[str, Any]):
        """Initialize with IR data."""
        self.ir_data = ir_data
        self.modules = ir_data.get('modules', [])
        self.metadata = ir_data.get('metadata', {})
        
        # Analysis results
        self.module_groups = {}
        self.dependency_graph = {}
        self.critical_modules = []
        self.architectural_layers = {}
        
        # Perform initial analysis
        self._analyze_modules()
        self._build_dependency_graph()
        self._identify_critical_modules()
        self._detect_architectural_layers()
    
    def _analyze_modules(self):
        """Analyze modules to identify patterns and groupings."""
        print("🔍 Analyzing module structure...")
        
        # Group modules by directory/namespace
        groups = defaultdict(list)
        
        for module in self.modules:
            module_name = module['name']
            file_path = module.get('file', '')
            
            # Extract directory structure
            if '\\' in file_path or '/' in file_path:
                # Get the directory path
                path_parts = file_path.replace('\\', '/').split('/')
                if len(path_parts) > 1:
                    # Use the first directory as the group
                    group = path_parts[0] if path_parts[0] != 'aider-main' else path_parts[1] if len(path_parts) > 1 else 'root'
                else:
                    group = 'root'
            else:
                group = 'root'
            
            groups[group].append(module)
        
        self.module_groups = dict(groups)
        print(f"   Found {len(self.module_groups)} module groups")
    
    def _build_dependency_graph(self):
        """Build a comprehensive dependency graph."""
        print("🔗 Building dependency graph...")
        
        graph = defaultdict(list)
        
        for module in self.modules:
            module_name = module['name']
            dependencies = module.get('dependencies', [])
            
            for dep in dependencies:
                dep_module = dep.get('module', '')
                strength = dep.get('strength', 'weak')
                
                # Only include internal dependencies (not external libraries)
                if self._is_internal_module(dep_module):
                    graph[module_name].append({
                        'target': dep_module,
                        'strength': strength
                    })
        
        self.dependency_graph = dict(graph)
        total_deps = sum(len(deps) for deps in self.dependency_graph.values())
        print(f"   Built graph with {total_deps} internal dependencies")
    
    def _identify_critical_modules(self):
        """Identify modules with high criticality or many dependencies."""
        print("⚠️  Identifying critical modules...")
        
        critical = []
        
        for module in self.modules:
            module_name = module['name']
            entities = module.get('entities', [])
            
            # Count high-criticality entities
            high_crit_count = sum(1 for e in entities if e.get('criticality') == 'high')
            total_entities = len(entities)
            
            # Count incoming dependencies
            incoming_deps = sum(1 for m in self.modules 
                              for dep in m.get('dependencies', [])
                              if dep.get('module') == module_name)
            
            # Calculate criticality score
            crit_score = (high_crit_count / max(total_entities, 1)) * 0.5 + (incoming_deps * 0.1)
            
            if crit_score > 0.3 or high_crit_count > 5 or incoming_deps > 3:
                critical.append({
                    'name': module_name,
                    'score': crit_score,
                    'high_crit_entities': high_crit_count,
                    'incoming_deps': incoming_deps,
                    'total_entities': total_entities
                })
        
        # Sort by criticality score
        self.critical_modules = sorted(critical, key=lambda x: x['score'], reverse=True)
        print(f"   Identified {len(self.critical_modules)} critical modules")
    
    def _detect_architectural_layers(self):
        """Detect architectural layers based on naming patterns and dependencies."""
        print("🏗️  Detecting architectural layers...")
        
        layers = {
            'presentation': [],
            'service': [],
            'core': [],
            'data': [],
            'utility': [],
            'test': [],
            'script': []
        }
        
        for module in self.modules:
            module_name = module['name'].lower()
            file_path = module.get('file', '').lower()
            
            # Classify based on naming patterns
            if any(keyword in module_name for keyword in ['ui', 'view', 'template', 'render', 'display']):
                layers['presentation'].append(module)
            elif any(keyword in module_name for keyword in ['service', 'api', 'endpoint', 'handler']):
                layers['service'].append(module)
            elif any(keyword in module_name for keyword in ['core', 'main', 'engine', 'pipeline']):
                layers['core'].append(module)
            elif any(keyword in module_name for keyword in ['data', 'model', 'entity', 'storage', 'db']):
                layers['data'].append(module)
            elif any(keyword in module_name for keyword in ['util', 'helper', 'tool', 'common']):
                layers['utility'].append(module)
            elif any(keyword in file_path for keyword in ['test', 'spec']):
                layers['test'].append(module)
            elif any(keyword in file_path for keyword in ['script', 'example', 'demo']):
                layers['script'].append(module)
            else:
                # Default to core if unclear
                layers['core'].append(module)
        
        # Remove empty layers
        self.architectural_layers = {k: v for k, v in layers.items() if v}
        print(f"   Detected {len(self.architectural_layers)} architectural layers")
    
    def _is_internal_module(self, module_name: str) -> bool:
        """Check if a module is internal to the project."""
        # Simple heuristic: internal modules don't contain common external library names
        external_patterns = [
            'os', 'sys', 'json', 'time', 'datetime', 'pathlib', 'typing',
            'collections', 're', 'math', 'random', 'itertools', 'functools',
            'argparse', 'logging', 'unittest', 'pytest', 'requests', 'numpy',
            'pandas', 'matplotlib', 'flask', 'django', 'fastapi'
        ]
        
        return not any(pattern in module_name.lower() for pattern in external_patterns)
    
    def _sanitize_name(self, name: str) -> str:
        """Sanitize names for Mermaid diagram compatibility."""
        # Replace problematic characters
        sanitized = re.sub(r'[^\w\-_]', '_', name)
        # Ensure it starts with a letter or underscore
        if sanitized and not sanitized[0].isalpha() and sanitized[0] != '_':
            sanitized = 'M_' + sanitized
        return sanitized or 'unknown'

    def generate_system_overview_diagram(self) -> str:
        """Generate a high-level system overview diagram."""
        print("📊 Generating system overview diagram...")

        mermaid = ["graph TB"]
        mermaid.append("    %% System Overview - Top Level Architecture")
        mermaid.append("")

        # Add module groups as subgraphs
        for group_name, modules in self.module_groups.items():
            if len(modules) < 2:  # Skip single-module groups for clarity
                continue

            sanitized_group = self._sanitize_name(group_name)
            mermaid.append(f"    subgraph {sanitized_group}[{group_name}]")

            # Add top modules from this group (limit for readability)
            top_modules = sorted(modules, key=lambda m: len(m.get('entities', [])), reverse=True)[:5]

            for module in top_modules:
                module_name = module['name']
                sanitized_name = self._sanitize_name(module_name)
                entity_count = len(module.get('entities', []))

                # Color code by entity count
                if entity_count > 50:
                    style = "fill:#ff6b6b,stroke:#d63031,color:#fff"  # Red for large
                elif entity_count > 20:
                    style = "fill:#feca57,stroke:#ff9ff3,color:#000"  # Yellow for medium
                else:
                    style = "fill:#48dbfb,stroke:#0abde3,color:#000"  # Blue for small

                mermaid.append(f"        {sanitized_name}[\"{module_name}<br/>({entity_count} entities)\"]")
                mermaid.append(f"        style {sanitized_name} {style}")

            mermaid.append("    end")
            mermaid.append("")

        # Add key dependencies between groups
        group_connections = defaultdict(set)
        for module in self.modules:
            module_group = self._get_module_group(module['name'])
            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if self._is_internal_module(dep_module):
                    dep_group = self._get_module_group(dep_module)
                    if module_group != dep_group and dep_group:
                        group_connections[module_group].add(dep_group)

        # Add group-level connections
        mermaid.append("    %% Group Dependencies")
        for source_group, target_groups in group_connections.items():
            for target_group in target_groups:
                if source_group in self.module_groups and target_group in self.module_groups:
                    source_sanitized = self._sanitize_name(source_group)
                    target_sanitized = self._sanitize_name(target_group)
                    mermaid.append(f"    {source_sanitized} --> {target_sanitized}")

        return "\n".join(mermaid)

    def generate_dependency_graph_diagram(self, max_modules: int = 30) -> str:
        """Generate a detailed dependency graph diagram."""
        print("🔗 Generating dependency graph diagram...")

        mermaid = ["graph LR"]
        mermaid.append("    %% Module Dependency Graph")
        mermaid.append("")

        # Get most connected modules
        module_scores = {}
        for module in self.modules:
            module_name = module['name']
            outgoing = len(module.get('dependencies', []))
            incoming = sum(1 for m in self.modules
                          for dep in m.get('dependencies', [])
                          if dep.get('module') == module_name)
            module_scores[module_name] = outgoing + incoming

        # Select top modules by connectivity
        top_modules = sorted(module_scores.items(), key=lambda x: x[1], reverse=True)[:max_modules]
        selected_modules = {name for name, _ in top_modules}

        # Add nodes with styling
        for module_name, score in top_modules:
            sanitized_name = self._sanitize_name(module_name)

            # Find the actual module data
            module_data = next((m for m in self.modules if m['name'] == module_name), None)
            if not module_data:
                continue

            entity_count = len(module_data.get('entities', []))

            # Style based on connectivity and size
            if score > 10:
                style = "fill:#ff6b6b,stroke:#d63031,color:#fff"  # High connectivity
            elif score > 5:
                style = "fill:#feca57,stroke:#ff9ff3,color:#000"  # Medium connectivity
            else:
                style = "fill:#48dbfb,stroke:#0abde3,color:#000"  # Low connectivity

            # Truncate long names
            display_name = module_name if len(module_name) <= 20 else module_name[:17] + "..."
            mermaid.append(f"    {sanitized_name}[\"{display_name}\"]")
            mermaid.append(f"    style {sanitized_name} {style}")

        mermaid.append("")

        # Add dependencies
        mermaid.append("    %% Dependencies")
        for module in self.modules:
            module_name = module['name']
            if module_name not in selected_modules:
                continue

            sanitized_source = self._sanitize_name(module_name)

            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if dep_module in selected_modules:
                    sanitized_target = self._sanitize_name(dep_module)
                    strength = dep.get('strength', 'weak')

                    # Style arrows by dependency strength
                    if strength == 'strong':
                        arrow_style = "stroke:#d63031,stroke-width:3px"
                    elif strength == 'medium':
                        arrow_style = "stroke:#ff9ff3,stroke-width:2px"
                    else:
                        arrow_style = "stroke:#74b9ff,stroke-width:1px"

                    mermaid.append(f"    {sanitized_source} --> {sanitized_target}")
                    # Note: Mermaid doesn't support per-edge styling in this syntax

        return "\n".join(mermaid)

    def _get_module_group(self, module_name: str) -> str:
        """Get the group name for a module."""
        for group_name, modules in self.module_groups.items():
            if any(m['name'] == module_name for m in modules):
                return group_name
        return 'unknown'

    def generate_component_architecture_diagram(self) -> str:
        """Generate a component architecture diagram showing layers."""
        print("🏗️  Generating component architecture diagram...")

        mermaid = ["graph TB"]
        mermaid.append("    %% Component Architecture - Layered View")
        mermaid.append("")

        # Define layer order (top to bottom)
        layer_order = ['presentation', 'service', 'core', 'data', 'utility']

        # Add layers as subgraphs
        for layer_name in layer_order:
            if layer_name not in self.architectural_layers:
                continue

            modules = self.architectural_layers[layer_name]
            if not modules:
                continue

            sanitized_layer = self._sanitize_name(layer_name)
            layer_display = layer_name.title() + " Layer"
            mermaid.append(f"    subgraph {sanitized_layer}[{layer_display}]")

            # Add key modules from this layer (limit for readability)
            key_modules = sorted(modules, key=lambda m: len(m.get('entities', [])), reverse=True)[:8]

            for module in key_modules:
                module_name = module['name']
                sanitized_name = self._sanitize_name(module_name)
                entity_count = len(module.get('entities', []))

                # Truncate long names
                display_name = module_name if len(module_name) <= 15 else module_name[:12] + "..."

                mermaid.append(f"        {sanitized_name}[\"{display_name}<br/>({entity_count})\"]")

            mermaid.append("    end")
            mermaid.append("")

        # Add layer dependencies
        mermaid.append("    %% Layer Dependencies")
        layer_deps = defaultdict(set)

        for layer_name, modules in self.architectural_layers.items():
            for module in modules:
                for dep in module.get('dependencies', []):
                    dep_module = dep.get('module', '')
                    if self._is_internal_module(dep_module):
                        dep_layer = self._get_module_layer(dep_module)
                        if dep_layer and dep_layer != layer_name:
                            layer_deps[layer_name].add(dep_layer)

        # Add layer connections
        for source_layer, target_layers in layer_deps.items():
            for target_layer in target_layers:
                if source_layer in self.architectural_layers and target_layer in self.architectural_layers:
                    source_sanitized = self._sanitize_name(source_layer)
                    target_sanitized = self._sanitize_name(target_layer)
                    mermaid.append(f"    {source_sanitized} --> {target_sanitized}")

        return "\n".join(mermaid)

    def generate_critical_path_diagram(self) -> str:
        """Generate a diagram showing critical modules and their relationships."""
        print("⚠️  Generating critical path diagram...")

        mermaid = ["graph TD"]
        mermaid.append("    %% Critical Path - High Impact Components")
        mermaid.append("")

        # Take top critical modules
        top_critical = self.critical_modules[:15]
        critical_names = {module['name'] for module in top_critical}

        # Add critical modules with styling
        for module_info in top_critical:
            module_name = module_info['name']
            sanitized_name = self._sanitize_name(module_name)
            score = module_info['score']
            high_crit = module_info['high_crit_entities']
            incoming = module_info['incoming_deps']

            # Style based on criticality score
            if score > 1.0:
                style = "fill:#ff4757,stroke:#ff3742,color:#fff"  # Critical
            elif score > 0.7:
                style = "fill:#ff6348,stroke:#ff4757,color:#fff"  # High
            elif score > 0.5:
                style = "fill:#ffa502,stroke:#ff9ff3,color:#000"  # Medium
            else:
                style = "fill:#fffa65,stroke:#ffdd59,color:#000"  # Low

            # Truncate long names
            display_name = module_name if len(module_name) <= 18 else module_name[:15] + "..."

            mermaid.append(f"    {sanitized_name}[\"{display_name}<br/>Score: {score:.2f}<br/>Critical: {high_crit}<br/>Used by: {incoming}\"]")
            mermaid.append(f"    style {sanitized_name} {style}")

        mermaid.append("")

        # Add dependencies between critical modules
        mermaid.append("    %% Critical Dependencies")
        for module in self.modules:
            module_name = module['name']
            if module_name not in critical_names:
                continue

            sanitized_source = self._sanitize_name(module_name)

            for dep in module.get('dependencies', []):
                dep_module = dep.get('module', '')
                if dep_module in critical_names:
                    sanitized_target = self._sanitize_name(dep_module)
                    strength = dep.get('strength', 'weak')

                    # Add dependency with strength indicator
                    if strength == 'strong':
                        mermaid.append(f"    {sanitized_source} ==> {sanitized_target}")
                    elif strength == 'medium':
                        mermaid.append(f"    {sanitized_source} --> {sanitized_target}")
                    else:
                        mermaid.append(f"    {sanitized_source} -.-> {sanitized_target}")

        return "\n".join(mermaid)

    def _get_module_layer(self, module_name: str) -> str:
        """Get the architectural layer for a module."""
        for layer_name, modules in self.architectural_layers.items():
            if any(m['name'] == module_name for m in modules):
                return layer_name
        return None

    def generate_all_diagrams(self, output_dir: str = "diagrams") -> Dict[str, str]:
        """Generate all diagram types and return their content."""
        print(f"\n🎨 Generating all architecture diagrams...")

        diagrams = {
            'system_overview': self.generate_system_overview_diagram(),
            'dependency_graph': self.generate_dependency_graph_diagram(),
            'component_architecture': self.generate_component_architecture_diagram(),
            'critical_path': self.generate_critical_path_diagram()
        }

        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        # Save diagrams to files
        for diagram_name, diagram_content in diagrams.items():
            file_path = output_path / f"{diagram_name}.mmd"
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(diagram_content)
            print(f"   Saved {diagram_name} to {file_path}")

        # Generate summary report
        self._generate_summary_report(output_path)

        return diagrams

    def _generate_summary_report(self, output_path: Path):
        """Generate a summary report of the architecture analysis."""
        report_path = output_path / "architecture_summary.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# System Architecture Analysis Summary\n\n")
            f.write(f"Generated from: {self.metadata.get('project_path', 'Unknown')}\n")
            f.write(f"Analysis date: {self.metadata.get('generated_at', 'Unknown')}\n\n")

            # Overall statistics
            f.write("## 📊 Overall Statistics\n\n")
            f.write(f"- **Total Modules**: {len(self.modules)}\n")
            f.write(f"- **Total Entities**: {self.metadata.get('total_entities', 'Unknown')}\n")
            f.write(f"- **Total Functions**: {self.metadata.get('total_functions', 'Unknown')}\n")
            f.write(f"- **Total Classes**: {self.metadata.get('total_classes', 'Unknown')}\n")
            f.write(f"- **Lines of Code**: {self.metadata.get('total_loc', 'Unknown')}\n")
            f.write(f"- **Dependencies**: {self.metadata.get('total_dependencies', 'Unknown')}\n\n")

            # Module groups
            f.write("## 🗂️ Module Groups\n\n")
            for group_name, modules in self.module_groups.items():
                f.write(f"### {group_name}\n")
                f.write(f"- **Modules**: {len(modules)}\n")
                total_entities = sum(len(m.get('entities', [])) for m in modules)
                f.write(f"- **Total Entities**: {total_entities}\n")
                f.write(f"- **Key Modules**: {', '.join([m['name'] for m in modules[:5]])}\n\n")

            # Architectural layers
            f.write("## 🏗️ Architectural Layers\n\n")
            for layer_name, modules in self.architectural_layers.items():
                f.write(f"### {layer_name.title()} Layer\n")
                f.write(f"- **Modules**: {len(modules)}\n")
                total_entities = sum(len(m.get('entities', [])) for m in modules)
                f.write(f"- **Total Entities**: {total_entities}\n\n")

            # Critical modules
            f.write("## ⚠️ Critical Modules\n\n")
            f.write("Top 10 most critical modules:\n\n")
            for i, module_info in enumerate(self.critical_modules[:10], 1):
                f.write(f"{i}. **{module_info['name']}**\n")
                f.write(f"   - Criticality Score: {module_info['score']:.2f}\n")
                f.write(f"   - High-Criticality Entities: {module_info['high_crit_entities']}\n")
                f.write(f"   - Incoming Dependencies: {module_info['incoming_deps']}\n")
                f.write(f"   - Total Entities: {module_info['total_entities']}\n\n")

            # Diagram descriptions
            f.write("## 📈 Generated Diagrams\n\n")
            f.write("### 1. System Overview (`system_overview.mmd`)\n")
            f.write("High-level view of module groups and their relationships. Shows the main architectural components and how they connect.\n\n")

            f.write("### 2. Dependency Graph (`dependency_graph.mmd`)\n")
            f.write("Detailed dependency relationships between the most connected modules. Color-coded by connectivity level.\n\n")

            f.write("### 3. Component Architecture (`component_architecture.mmd`)\n")
            f.write("Layered architecture view showing presentation, service, core, data, and utility layers.\n\n")

            f.write("### 4. Critical Path (`critical_path.mmd`)\n")
            f.write("Focus on the most critical modules and their dependencies. Useful for understanding high-impact components.\n\n")

            f.write("## 🔧 How to View Diagrams\n\n")
            f.write("1. **Online**: Copy the `.mmd` file content to [Mermaid Live Editor](https://mermaid.live/)\n")
            f.write("2. **VS Code**: Install the Mermaid Preview extension\n")
            f.write("3. **Command Line**: Use `mmdc` (mermaid-cli) to generate images\n")
            f.write("4. **Documentation**: Include in Markdown files that support Mermaid\n\n")

        print(f"   Generated summary report: {report_path}")


def load_ir_data(file_path: str) -> Dict[str, Any]:
    """Load IR data from JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: IR file not found: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON in IR file: {e}")
        return None


def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(
        description="Generate system architecture diagrams from Mid-Level IR JSON data"
    )
    parser.add_argument(
        'ir_file',
        nargs='?',
        default='complete_mid_level_ir.json',
        help='Path to the IR JSON file (default: complete_mid_level_ir.json)'
    )
    parser.add_argument(
        '--output-dir',
        default='diagrams',
        help='Output directory for generated diagrams (default: diagrams)'
    )
    parser.add_argument(
        '--diagram-type',
        choices=['all', 'overview', 'dependencies', 'components', 'critical'],
        default='all',
        help='Type of diagram to generate (default: all)'
    )

    args = parser.parse_args()

    print("🎨 System Architecture Diagram Generator")
    print("=" * 50)

    # Load IR data
    print(f"📂 Loading IR data from: {args.ir_file}")
    ir_data = load_ir_data(args.ir_file)
    if not ir_data:
        return 1

    # Initialize generator
    generator = ArchitectureDiagramGenerator(ir_data)

    # Generate diagrams based on type
    if args.diagram_type == 'all':
        diagrams = generator.generate_all_diagrams(args.output_dir)
        print(f"\n✅ Generated {len(diagrams)} architecture diagrams in '{args.output_dir}' directory")
    else:
        # Generate specific diagram type
        diagram_methods = {
            'overview': generator.generate_system_overview_diagram,
            'dependencies': generator.generate_dependency_graph_diagram,
            'components': generator.generate_component_architecture_diagram,
            'critical': generator.generate_critical_path_diagram
        }

        method = diagram_methods[args.diagram_type]
        diagram_content = method()

        # Save single diagram
        output_path = Path(args.output_dir)
        output_path.mkdir(exist_ok=True)
        file_path = output_path / f"{args.diagram_type}.mmd"

        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(diagram_content)

        print(f"\n✅ Generated {args.diagram_type} diagram: {file_path}")

    print("\n🔧 To view the diagrams:")
    print("   1. Copy .mmd content to https://mermaid.live/")
    print("   2. Use VS Code with Mermaid Preview extension")
    print("   3. Use mermaid-cli: mmdc -i diagram.mmd -o diagram.png")

    return 0


if __name__ == "__main__":
    exit(main())
